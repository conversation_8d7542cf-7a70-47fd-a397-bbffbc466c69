<?php

declare(strict_types=1);

namespace MailScanModule\ApiClient;

use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Dto\PostItemBag;

interface IMailroomApiClient
{
    public function getPostItemById(string $postItemId): ?MailroomPostItemData;

    public function getPostItemsByCompanyNumberList(array $companyNumbers, array $pagination = []): ?PostItemBag;

    public function getUnreleasedNonStatutoryPostItems(): ?PostItemBag;

    public function getUnprocessedPostItems(?int $limit = null, ?string $lastId = null): ?PostItemBag;

    public function getRequireUpdatePostItems(): ?PostItemBag;

    public function setPostItemStatus(array $items, ?string $status = null): array;

    public function setNonStatutoryPostItemAsReleased(string $postItemId): bool;

    public function setNonStatutoryPostItemOrderId(array $orderIds): bool;

    public function setLastEmailSent(array $postItems): bool;

    public function setItemsToAboveQuota(array $itemsAboveQuota): bool;

    public function setPostItemsAsReleased(array $items): bool;

    public function setFailedChargeAttempts(array $items): bool;

    public function setPostItemsAsRts(array $items): bool;

    public function setPostItemsAsWaitingForPayment(array $items): bool;

    public function addPostItem(
        string $companyNumber,
        string $companyName,
        string $typeMail = 'statutory',
        string $sender = 'OTHER',
        string $fileName = 'N/A',
        string $operator = 'cms',
        string $batch = 'N/A',
        array $events = [],
        array $details = [],
    );
}
