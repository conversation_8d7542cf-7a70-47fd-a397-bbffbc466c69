<?php

declare(strict_types=1);

namespace MailScanModule\ApiClient;

use Doctrine\Common\Collections\ArrayCollection;
use Firebase\JWT\JWT;
use HttpClient\ClientInterface;
use HttpClient\Exceptions\RequestException;
use Http<PERSON>lient\Requests\Request;
use HttpClient\Requests\RequestInterface;
use HttpClient\Requests\RequestOptions;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Dto\PostItemBag;
use MailScanModule\Enums\StatusEnum;
use MailScanModule\Factories\MailroomPostItemDataFactory;
use MailScanModule\Factories\PostItemBagFactory;
use MailScanModule\Responses\Response;
use Utils\Helpers\ArrayHelper;

class MailroomApiClient implements IMailroomApiClient
{
    public const CHARGING_ATTEMPTS_DETAIL_NAME = 'charging_attempts';
    public const HANDLING_FEE_DETAIL_NAME = 'handling_fee';
    public const FORWARDING_FEE_DETAIL_NAME = 'forwarding_fee';
    public const EXTRA_QUOTA_FEE_DETAIL_NAME = 'extra_quota_fee';
    public const LAST_EMAIL_SENT_DETAIL_NAME = 'last_email_sent';
    public const PAY_TO_RELEASE_FEE_DETAIL_NAME = 'pay_to_release_fee';
    public const CUSTOM_PRICE_DETAIL_NAME = 'custom_price';
    public const REGION_UK = 'uk';
    public const REGION_EUROPE = 'europe';
    public const REGION_WORLD = 'world';
    private const ADD_POST_ITEM = 'add-post-item-external';
    private const ADD_POST_ITEM_DETAIL = 'add-post-item-detail';
    private const ADD_POST_ITEM_EVENT = 'add-post-item-event';
    private const CHANGE_ITEM_STATUS = 'change-item-status';
    private const GET_POST_ITEM = 'get-post-item';
    private const GET_POST_ITEMS = 'get-post-items';
    private const GET_FAILED_PAYMENT_POST_ITEMS = 'get-failed-payment-post-items';
    private const MARK_ITEMS_AS_CHARGED = 'mark-items-as-charged';
    private const SET_ITEMS_TO_ABOVE_QUOTA = 'set-items-to-above-quota';
    private const SET_LAST_EMAIL_SENT = 'set-last-email-sent';
    private const SET_POST_ITEM_AS_RELEASED = 'set-post-item-as-released';
    private const UNRELEASED_NON_STATUTORY_POST_ITEMS = 'get-waiting-non-statutory-post-items';
    private const UNPROCESSED_POST_ITEMS = 'get-unprocessed-post-items';
    private const POST_RETURNED_TO_SENDER_EVENT_NAME = 'post_to_be_returned_to_sender';
    private const AWAITING_PAYMENT_EVENT_NAME = 'awaiting_payment';
    private const MARK_AS_PURCHASED = 'mark-item-as-purchased';
    private const GET_PARCEL_TRANSIT_FEE = 'get-parcel-transit-fee';
    private const CHUNK_SIZE = 100;
    private const OPERATOR_CMS = 'cms';
    private const ONE_HOUR = 3600;
    private const REQUIRE_UPDATE_POST_ITEMS = 'get-require-update-post-items';
    private const DEFAULT_TIMEOUT = 60;

    public function __construct(
        private readonly ClientInterface $httpClient,
        private readonly IResponseResolver $responseResolver,
        private readonly string $mailroomApiToken,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function getPostItemById(string $postItemId): ?MailroomPostItemData
    {
        if (empty($postItemId)) {
            throw new \Exception('Argument postItemId missing.');
        }

        $response = $this->makeRequest(
            endpoint: self::GET_POST_ITEM,
            query: ['post_item_id' => $postItemId],
            actionErrorMessage: 'getting post item by id'
        );

        return MailroomPostItemDataFactory::createFromMailroomApiResponseItem(
            ArrayHelper::get(json_decode($response->getBody(), true), 'data')
        );
    }

    /**
     * @throws \Exception
     */
    public function getPostItemsByCompanyNumberList(array $companyNumbers, array $pagination = []): PostItemBag
    {
        if (empty($companyNumbers)) {
            return new PostItemBag(new ArrayCollection([]));
        }

        $companyNumbers = array_filter($companyNumbers, fn ($value) => !empty($value));

        $body = ['company_numbers' => $companyNumbers];

        if (!empty($pagination)) {
            if (isset($pagination['pnum'])) {
                $body['page_number'] = $pagination['pnum'];
            }
            if (isset($pagination['psize'])) {
                $body['page_size'] = $pagination['psize'];
            }
        }

        $response = $this->makeRequest(
            endpoint: self::GET_POST_ITEMS,
            method: RequestInterface::POST,
            body: $body,
            actionErrorMessage: 'getting post items from company numbers list'
        );

        return PostItemBagFactory::createFromMailroomApiResponse($response);
    }

    /**
     * @throws \Exception
     */
    public function getPostItemsForCompany(string $companyNumber, array $pagination = []): array
    {
        $body = ['company_numbers' => [$companyNumber]];

        if (!empty($pagination)) {
            if (isset($pagination['pnum'])) {
                $body['page_number'] = $pagination['pnum'];
            }
            if (isset($pagination['psize'])) {
                $body['page_size'] = $pagination['psize'];
            }
        }

        $response = $this->makeRequest(
            endpoint: self::GET_POST_ITEMS,
            method: RequestInterface::POST,
            body: $body,
            actionErrorMessage: 'getting mailroom post items'
        );

        return [
            'postItems' => PostItemBagFactory::createFromMailroomApiResponse($response),
            'total' => ArrayHelper::get(json_decode($response->getBody(), true), 'total_items', 0),
        ];
    }

    /**
     * @throws \Exception
     */
    public function getFailedPaymentPostItems(array $companyNumbers = []): array
    {
        $postItemsArray = [];

        foreach (array_chunk($companyNumbers, self::CHUNK_SIZE) as $chunk) {
            $response = $this->makeRequest(
                endpoint: self::GET_FAILED_PAYMENT_POST_ITEMS,
                query: ['cnum' => $chunk],
                actionErrorMessage: 'getting unpaid forwarded items'
            );

            $currentBag = PostItemBagFactory::createFromMailroomApiResponse($response);
            $postItemsArray = array_merge($postItemsArray, $currentBag->getAllPostItems());
        }

        return $postItemsArray;
    }

    /**
     * @throws \Exception
     */
    public function getUnreleasedNonStatutoryPostItems(): PostItemBag
    {
        $response = $this->makeRequest(
            endpoint: self::UNRELEASED_NON_STATUTORY_POST_ITEMS,
            actionErrorMessage: 'getting unreleased non-statutory post items'
        );

        return PostItemBagFactory::createFromMailroomApiResponse($response);
    }

    /**
     * @throws \Exception
     */
    public function getUnprocessedPostItems(?int $limit = null, ?string $lastId = null): PostItemBag
    {
        $query = [];

        if ($limit !== null) {
            $query['limit'] = $limit;
        }

        if ($lastId !== null) {
            $query['lastId'] = $lastId;
        }

        $response = $this->makeRequest(
            endpoint: self::UNPROCESSED_POST_ITEMS,
            query: $query,
            actionErrorMessage: 'getting unprocessed post items'
        );

        return PostItemBagFactory::createFromMailroomApiResponse($response);
    }

    /**
     * @throws \Exception
     */
    public function getParcelTransitFee(MailroomPostItemData $postItem, string $region): float
    {
        $response = $this->makeRequest(
            endpoint: self::GET_PARCEL_TRANSIT_FEE,
            query: [
                'post_item_id' => $postItem->getId(),
                'region' => $region,
            ],
            actionErrorMessage: 'getting parcel transit fee'
        );

        $body = json_decode($response->getBody(), true);
        if (empty($body)) {
            throw new \Exception('Response body is empty');
        }

        $transitFee = ArrayHelper::get($body, ['data', 'transit_fee'], null);
        if (empty($transitFee)) {
            throw new \Exception('Transit fee is empty');
        }

        return floatval($transitFee);
    }

    /**
     * @throws \Exception
     */
    public function getRequireUpdatePostItems(): PostItemBag
    {
        $response = $this->makeRequest(
            endpoint: self::REQUIRE_UPDATE_POST_ITEMS,
            actionErrorMessage: 'getting require update post items'
        );

        return PostItemBagFactory::createFromMailroomApiResponse($response);
    }

    /**
     * @throws \Exception
     *
     * @deprecated use setPostItemsAsReleased
     */
    public function setNonStatutoryPostItemAsReleased(string $postItemId): bool
    {
        $this->makeRequest(
            endpoint: self::SET_POST_ITEM_AS_RELEASED,
            method: RequestInterface::PUT,
            body: ['post_item_ids' => [$postItemId]],
            actionErrorMessage: 'setting post item as released'
        );

        return true;
    }

    /**
     * @param MailroomPostItemData[] $items
     *
     * @throws \Exception
     */
    public function setPostItemsAsReleased(array $items): bool
    {
        $body = array_map(
            fn (MailroomPostItemData $item) => [
                'post_item_id' => $item->getId(),
                'status_to_set' => $item->getStatus(),
            ],
            $items
        );

        $this->makeRequest(
            endpoint: self::SET_POST_ITEM_AS_RELEASED,
            method: RequestInterface::PUT,
            body: $body,
            actionErrorMessage: 'setting post items as released'
        );

        return true;
    }

    /**
     * @param MailroomPostItemData[] $items
     *
     * @throws \Exception
     */
    public function setPostItemStatus(array $items, ?string $status = null): array
    {
        $body = [];
        foreach ($items as $item) {
            if (!is_null($status)) {
                $body[$item->getId()] = $status;
                continue;
            }

            if (!is_null($item->getDesiredStatus())) {
                $body[$item->getId()] = $item->getDesiredStatus();
                $item->setStatus($item->getDesiredStatus());
            }
        }

        if (empty($body)) {
            return [];
        }

        $response = $this->makeRequest(
            endpoint: self::CHANGE_ITEM_STATUS,
            method: RequestInterface::PUT,
            body: ['post_item_ids' => $body],
            actionErrorMessage: 'setting post item status'
        );

        $body = json_decode($response->getBody(), true);
        if (empty($body)) {
            throw new \Exception('Response body is empty');
        }

        return $body;
    }

    /**
     * @throws RequestException
     * @throws \Exception
     */
    public function setFailedChargeAttempts(array $items): bool
    {
        $payload = [];
        /** @var MailroomPostItemData $item */
        foreach ($items as $item) {
            $payload[] = [
                'post_item_id' => $item->getId(),
                'detail_key'   => self::CHARGING_ATTEMPTS_DETAIL_NAME,
                'detail_value' => intval($item->getDetail(self::CHARGING_ATTEMPTS_DETAIL_NAME)),
            ];

            $payload[] = [
                'post_item_id' => $item->getId(),
                'detail_key'   => self::HANDLING_FEE_DETAIL_NAME,
                'detail_value' => floatval($item->getDetail(self::HANDLING_FEE_DETAIL_NAME)),
            ];

            $payload[] = [
                'post_item_id' => $item->getId(),
                'detail_key'   => self::FORWARDING_FEE_DETAIL_NAME,
                'detail_value' => floatval($item->getDetail(self::FORWARDING_FEE_DETAIL_NAME)),
            ];

            $payload[] = [
                'post_item_id' => $item->getId(),
                'detail_key'   => self::EXTRA_QUOTA_FEE_DETAIL_NAME,
                'detail_value' => floatval($item->getDetail(self::EXTRA_QUOTA_FEE_DETAIL_NAME)),
            ];
        }

        $this->makeRequest(
            endpoint: self::ADD_POST_ITEM_DETAIL,
            method: RequestInterface::POST,
            body: $payload,
            actionErrorMessage: 'adding post item detail'
        );

        return true;
    }

    /**
     * @throws RequestException
     */
    public function setPostItemsAsRts(array $items): bool
    {
        $this->requestPostItemEventAddition(
            array_map(fn (MailroomPostItemData $item) => [
                'post_item_id'  => $item->getId(),
                'event_name'    => self::POST_RETURNED_TO_SENDER_EVENT_NAME,
                'operator'      => self::OPERATOR_CMS,
                'status_to_set' => StatusEnum::STATUS_TO_BE_RTS->value,
            ], $items)
        );

        return true;
    }

    /**
     * @throws RequestException
     */
    public function setPostItemsAsWaitingForPayment(array $items): bool
    {
        $this->requestPostItemEventAddition(
            array_map(fn (MailroomPostItemData $item) => [
                'post_item_id'       => $item->getId(),
                'event_name'         => self::AWAITING_PAYMENT_EVENT_NAME,
                'operator'           => self::OPERATOR_CMS,
                'status_to_set'      => StatusEnum::STATUS_WAITING_PAYMENT->value,
                'details_to_add'     => ['pay_to_release_fee' => $item->getDetail(self::PAY_TO_RELEASE_FEE_DETAIL_NAME)],
            ], $items)
        );

        return true;
    }

    /**
     * @throws \Exception
     */
    public function setNonStatutoryPostItemOrderId(array $orderIds): bool
    {
        if (empty($orderIds)) {
            return true;
        }

        $response = $this->makeRequest(
            endpoint: self::MARK_ITEMS_AS_CHARGED,
            method: RequestInterface::PUT,
            body: ['post_items' => $orderIds],
            actionErrorMessage: 'marking items as charged'
        );

        $results = ArrayHelper::get(json_decode($response->getBody(), true), 'results', []);

        foreach ($results as $key => $result) {
            if ($result == 0) {
                throw new \Exception(sprintf('Error setting order_id on an item in the Mailroom db: %s', $key));
            }
        }

        return true;
    }

    /**
     * @throws \Exception
     */
    public function setLastEmailSent(array $postItems): bool
    {
        if (empty($postItems)) {
            return true;
        }

        $this->makeRequest(
            endpoint: self::SET_LAST_EMAIL_SENT,
            method: RequestInterface::PUT,
            body: ['post_items' => $postItems],
            actionErrorMessage: 'setting last email sent'
        );

        return true;
    }

    /**
     * @throws \Exception
     */
    public function setItemsToAboveQuota(array $itemsAboveQuota): bool
    {
        if (empty($itemsAboveQuota)) {
            return true;
        }

        $this->makeRequest(
            endpoint: self::SET_ITEMS_TO_ABOVE_QUOTA,
            method: RequestInterface::PUT,
            body: [
                'post_items' => array_map(
                    fn (MailroomPostItemData $item) => $item->getId(),
                    $itemsAboveQuota
                ),
            ],
            actionErrorMessage: 'setting items to above quota'
        );

        return true;
    }

    /**
     * @throws \Exception
     */
    public function addPostItem(
        string $companyNumber,
        string $companyName,
        string $typeMail = 'statutory',
        string $sender = 'OTHER',
        string $fileName = 'N/A',
        string $operator = self::OPERATOR_CMS,
        string $batch = 'N/A',
        array $events = [],
        array $details = [],
    ): void {
        $payload = [
            'type_mail' => $typeMail,
            'companyNumber' => $companyNumber,
            'companyName' => $companyName,
            'batchId' => $batch,
            'pdf_filename' => $fileName,
            'sender' => $sender,
            'operator' => $operator,
        ];

        if (!empty($events)) {
            $payload['events'] = $events;
        }

        if (!empty($details)) {
            $payload['details'] = $details;
        }

        $this->makeRequest(
            endpoint: self::ADD_POST_ITEM,
            method: RequestInterface::POST,
            body: $payload,
            actionErrorMessage: 'adding post item'
        );
    }

    /**
     * @throws \Exception
     */
    public function purchaseItem(array $payload): void
    {
        $this->makeRequest(
            endpoint: self::MARK_AS_PURCHASED,
            method: RequestInterface::PUT,
            body: $payload,
            actionErrorMessage: 'marking item as purchased'
        );
    }

    /**
     * @throws \Exception
     */
    private function requestPostItemEventAddition(array $payload): void
    {
        $this->makeRequest(
            endpoint: self::ADD_POST_ITEM_EVENT,
            method: RequestInterface::POST,
            body: $payload,
            actionErrorMessage: 'adding post item event'
        );
    }

    private function makeRequest(
        string $endpoint,
        string $method = RequestInterface::GET,
        int $timeout = self::DEFAULT_TIMEOUT,
        ?array $body = null,
        array $query = [],
        string $actionErrorMessage = '',
    ): Response {
        try {
            $response = $this->responseResolver->resolveResponse($this->httpClient->sendRequest(
                new Request(
                    $endpoint,
                    $method,
                    $this->generateJwtHeaderToken(),
                    !is_null($body) ? json_encode($body) : null,
                    new RequestOptions(timeout: $timeout),
                    $query
                )
            ));
        } catch (RequestException $e) {
            throw new \Exception(sprintf('Error while %s. Code: %s | Error: %s', $actionErrorMessage, $e->getCode(), $e->getMessage()));
        } catch (\Throwable $e) {
            throw new \Exception(sprintf('Unforeseen exception while %s. Code: %s | Error: %s', $actionErrorMessage, $e->getCode(), $e->getMessage()));
        }

        if (!$response->isSuccess()) {
            throw new \Exception(sprintf('The request failed while %s. Code: %s | Body: %s', $actionErrorMessage, $response->getCode(), $response->getBody()));
        }

        return $response;
    }

    private function generateJwtHeaderToken(): array
    {
        return [
            'x-api-auth' => JWT::encode(
                [
                    'iat' => time(),
                    'exp' => time() + self::ONE_HOUR,
                    'accessed_by' => self::OPERATOR_CMS,
                ],
                $this->mailroomApiToken
            ),
        ];
    }
}
